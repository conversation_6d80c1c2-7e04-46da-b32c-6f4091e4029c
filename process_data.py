import pandas as pd
import os
import matplotlib.pyplot as plt
import seaborn as sns
import sys

def process_data(file_path):
    # 读取原始数据
    with open(file_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()

    processed_data = []
    current_machine = None

    for line in lines:
        # 检测当前是哪台机器
        if line.startswith('Machine'):
            current_machine = line.strip()
            continue

        # 跳过包含 'Got BLE CMD1' 的行
        if 'Got BLE CMD1' in line:
            continue

        # 提取 D: G: B: R: 数据
        if 'G:' in line and 'B:' in line and 'R:' in line:
            parts = line.strip().split()
            g_value = float(parts[2].replace('G:', ''))
            b_value = float(parts[3].replace('B:', ''))
            r_value = float(parts[4].replace('R:', '').split()[0])

            processed_data.append([
                current_machine,
                g_value,
                b_value,
                r_value
            ])

    # 调试输出前5条数据
    print("提取的部分数据：")
    for i, data in enumerate(processed_data[:5]):
        print(f"{i+1}: {data}")

    # 创建DataFrame
    df = pd.DataFrame(processed_data, columns=['Machine', 'G', 'B', 'R'])

    # 导出到Excel
    excel_path = os.path.join(os.path.dirname(file_path), 'processed_data.xlsx')
    df.to_excel(excel_path, index=False)

    # 数据可视化
    plt.figure(figsize=(14, 8))

    # 将数据转换为长格式，便于绘图
    df_long = pd.melt(df, id_vars=['Machine'], value_vars=['G', 'B', 'R'],
                     var_name='Parameter', value_name='Value')

    # 添加一个组合标签用于图例
    df_long['Legend'] = df_long['Machine'] + ' - ' + df_long['Parameter']

    # 绘图：每个组合使用不同颜色
    sns.lineplot(x=df_long.index, y='Value', hue='Legend', data=df_long, marker='o')

    plt.title('不同机器的 G、B、R 值变化趋势')
    plt.xlabel('测试次数')
    plt.ylabel('数值')
    plt.legend(title='机器 - 参数')
    plt.xticks(rotation=45)
    plt.tight_layout()

    # 保存图表
    chart_path = os.path.join(os.path.dirname(file_path), 'data_chart.png')
    plt.savefig(chart_path)
    plt.close()

    return excel_path, chart_path


def process_experiment_data(file_path, output_excel_path, output_chart_path, machine_name):
    """
    处理实验数据并生成图表
    :param file_path: 原始数据文件路径
    :param output_excel_path: 输出Excel文件路径
    :param output_chart_path: 输出图表路径
    :param machine_name: 机器名称 (如 "MachineA", "MachineB", "Machinea", "Machineb")
    :return: None
    """
    # 读取原始数据
    with open(file_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()

    processed_data = []

    for line in lines:
        if 'Got BLE CMD49' in line:
            continue

        if 'CH:1' in line:
            # 提取 D: G: B: 数据
            parts = line.strip().split()
            d_value = float(parts[2].replace('D:', '').replace(',', '.'))
            g_value = float(parts[3].replace('G:', '').replace(',', '.'))
            b_value = float(parts[4].replace('B:', '').replace(',', '.'))

            processed_data.append([d_value, g_value, b_value])

    # 创建DataFrame
    df = pd.DataFrame(processed_data, columns=['D', 'G', 'B'])

    # 添加机器列
    df['Machine'] = machine_name

    # 导出到Excel
    if not os.path.exists(output_excel_path):
        # 如果文件不存在，则创建新文件并写入表头
        df.to_excel(output_excel_path, index=False, header=True)
    else:
        # 如果文件存在，则追加数据且不写入表头
        with pd.ExcelWriter(output_excel_path, mode='a', engine='openpyxl') as writer:
            df.to_excel(writer, index=False, header=False)

    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei']  # 使用黑体显示中文
    plt.rcParams['axes.unicode_minus'] = False  # 正常显示负号

    # 绘制折线图
    plt.figure(figsize=(14, 8))
    
    # 明确绘制每条线并设置标签
    sns.lineplot(x=df.index, y='D', data=df, label='D 值')
    sns.lineplot(x=df.index, y='G', data=df, label='G 值')
    sns.lineplot(x=df.index, y='B', data=df, label='B 值')

    plt.title('实验数据变化趋势')
    plt.xlabel('测试次数')
    plt.ylabel('数值')
    plt.legend()
    plt.xticks(rotation=45)
    plt.tight_layout()

    # 保存图表
    plt.savefig(output_chart_path)
    plt.close()

def filter_and_format_data(file_path, output_excel_path=None):
    """
    过滤并格式化数据，保存到Excel文件

    :param file_path: 输入文件路径
    :param output_excel_path: 输出Excel文件路径（可选，如果不提供则只打印到控制台）
    :return: 格式化后的数据字典和数值数据
    """
    # 读取原始数据
    with open(file_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()

    formatted_data = {}
    numeric_data = {}  # 存储数值数据用于绘图
    excel_data = []    # 存储用于Excel的数据
    current_machine = None
    test_count = {}    # 记录每台机器的测试次数

    for line in lines:
        line = line.strip()

        # 检测当前是哪台机器
        if line.startswith('Machine'):
            current_machine = line
            if current_machine not in formatted_data:
                formatted_data[current_machine] = []
                numeric_data[current_machine] = {'G': [], 'B': [], 'R': []}
                test_count[current_machine] = 0
            continue

        # 跳过包含 'Got BLE CMD1' 的行
        if 'Got BLE CMD1' in line:
            continue

        # 提取 G: B: R: 数据
        if 'G:' in line and 'B:' in line and 'R:' in line and current_machine:
            parts = line.split()

            # 找到G、B、R的值
            g_value = None
            b_value = None
            r_value = None

            for part in parts:
                if part.startswith('G:'):
                    g_value = part.replace('G:', '')
                elif part.startswith('B:'):
                    b_value = part.replace('B:', '')
                elif part.startswith('R:'):
                    r_value = part.replace('R:', '')

            if g_value and b_value and r_value:
                # 格式化字符串
                formatted_line = f"G:{g_value}   B:{b_value}   R:{r_value}"
                formatted_data[current_machine].append(formatted_line)

                # 存储数值数据
                try:
                    g_float = float(g_value)
                    b_float = float(b_value)
                    r_float = float(r_value)

                    numeric_data[current_machine]['G'].append(g_float)
                    numeric_data[current_machine]['B'].append(b_float)
                    numeric_data[current_machine]['R'].append(r_float)

                    # 为Excel准备数据
                    test_count[current_machine] += 1
                    excel_data.append({
                        '机器名称': current_machine,
                        '测试次数': test_count[current_machine],
                        'G值': g_float,
                        'B值': b_float,
                        'R值': r_float
                    })

                except ValueError:
                    print(f"警告：无法转换数值 G:{g_value}, B:{b_value}, R:{r_value}")

    # 输出结果到控制台
    print("=" * 50)
    print("过滤并格式化后的数据：")
    print("=" * 50)

    for machine, data_lines in formatted_data.items():
        print(f"\n{machine}")
        for data_line in data_lines:
            print(data_line)

    # 如果提供了输出Excel文件路径，则保存到Excel
    if output_excel_path and excel_data:
        df = pd.DataFrame(excel_data)

        # 创建Excel写入器
        with pd.ExcelWriter(output_excel_path, engine='openpyxl') as writer:
            # 写入主数据表
            df.to_excel(writer, sheet_name='机器数据', index=False)

            # 为每台机器创建单独的工作表
            for machine in formatted_data.keys():
                machine_data = df[df['机器名称'] == machine].copy()
                # 清理工作表名称（Excel工作表名称不能包含某些字符）
                sheet_name = machine.replace(':', '_').replace('/', '_')[:31]  # Excel工作表名称限制31个字符
                machine_data.to_excel(writer, sheet_name=sheet_name, index=False)

        print(f"\n数据已保存到Excel文件: {output_excel_path}")
        print(f"Excel文件包含 {len(formatted_data) + 1} 个工作表：")
        print("  - 机器数据（所有数据汇总）")
        for machine in formatted_data.keys():
            print(f"  - {machine}（单独数据）")

    return formatted_data, numeric_data

def create_line_charts(numeric_data, output_dir):
    """
    创建3个折线图（G、B、R），每个机器用不同颜色的折线

    :param numeric_data: 包含各机器G、B、R数值的字典
    :param output_dir: 输出目录
    :return: 生成的图表文件路径列表
    """
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei']  # 使用黑体显示中文
    plt.rcParams['axes.unicode_minus'] = False  # 正常显示负号

    # 定义颜色映射
    colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b']

    chart_files = []
    parameters = ['G', 'B', 'R']

    for param in parameters:
        plt.figure(figsize=(12, 8))

        color_idx = 0
        for machine, data in numeric_data.items():
            if param in data and len(data[param]) > 0:
                # 测试次数作为横坐标
                test_numbers = list(range(1, len(data[param]) + 1))

                # 绘制折线图
                plt.plot(test_numbers, data[param],
                        marker='o',
                        linewidth=2,
                        markersize=6,
                        color=colors[color_idx % len(colors)],
                        label=machine)

                color_idx += 1

        # 设置图表属性
        plt.title(f'{param} 值变化趋势', fontsize=16, fontweight='bold')
        plt.xlabel('测试次数', fontsize=12)
        plt.ylabel(f'{param} 数值', fontsize=12)
        plt.legend(title='机器', fontsize=10, title_fontsize=12)
        plt.grid(True, alpha=0.3)

        # 设置坐标轴
        plt.xticks(fontsize=10)
        plt.yticks(fontsize=10)

        # 调整布局
        plt.tight_layout()

        # 保存图表
        chart_file = os.path.join(output_dir, f'{param}_line_chart.png')
        plt.savefig(chart_file, dpi=300, bbox_inches='tight')
        chart_files.append(chart_file)

        # 显示图表（可选）
        # plt.show()

        # 关闭图表以释放内存
        plt.close()

        print(f'{param} 折线图已保存到: {chart_file}')

    return chart_files

def generate_charts_only(file_path=None):
    """
    仅生成图表的函数，不保存Excel文件

    :param file_path: 输入文件路径，如果为None则使用默认路径
    """
    if file_path is None:
        file_path = r'c:\Users\<USER>\Desktop\交接资料\测试数据\data.txt'

    output_dir = os.path.dirname(file_path)

    print(f'正在从文件生成图表: {file_path}')

    try:
        # 只获取数值数据，不保存Excel文件
        _, numeric_data = filter_and_format_data(file_path, None)

        # 生成折线图
        print(f'正在生成折线图...')
        chart_files = create_line_charts(numeric_data, output_dir)

        print(f'图表生成完成！共生成了 {len(chart_files)} 个图表文件')
        return chart_files

    except Exception as e:
        print(f'生成图表时发生错误: {str(e)}')
        return []

def read_excel_data(excel_file_path):
    """
    读取并显示Excel文件的内容

    :param excel_file_path: Excel文件路径
    """
    try:
        # 读取所有工作表
        excel_file = pd.ExcelFile(excel_file_path)

        print(f"Excel文件: {excel_file_path}")
        print(f"包含工作表: {excel_file.sheet_names}")
        print("=" * 60)

        # 读取主数据表
        main_df = pd.read_excel(excel_file_path, sheet_name='机器数据')
        print("\n主数据表（前10行）:")
        print(main_df.head(10).to_string(index=False))

        # 显示数据统计
        print(f"\n数据统计:")
        print(f"总记录数: {len(main_df)}")
        print(f"机器数量: {main_df['机器名称'].nunique()}")

        # 按机器分组统计
        machine_stats = main_df.groupby('机器名称').agg({
            '测试次数': 'count',
            'G值': ['mean', 'min', 'max'],
            'B值': ['mean', 'min', 'max'],
            'R值': ['mean', 'min', 'max']
        }).round(2)

        print(f"\n各机器统计信息:")
        print(machine_stats.to_string())

    except Exception as e:
        print(f"读取Excel文件时发生错误: {str(e)}")

def main():
    # 默认输入文件路径
    input_file = r'c:\Users\<USER>\Desktop\交接资料\测试数据\data.txt'

    # 输出文件路径
    output_excel_file = os.path.join(os.path.dirname(input_file), 'filtered_data.xlsx')
    output_dir = os.path.dirname(input_file)

    print(f'正在处理文件: {input_file}')

    try:
        # 过滤并格式化数据，保存到Excel
        formatted_data, numeric_data = filter_and_format_data(input_file, output_excel_file)

        print(f'\n处理完成！')
        print(f'共处理了 {len(formatted_data)} 台机器的数据')

        # 显示每台机器的数据条数
        for machine, data_lines in formatted_data.items():
            print(f'{machine}: {len(data_lines)} 条数据')

        # 生成折线图
        print(f'\n正在生成折线图...')
        chart_files = create_line_charts(numeric_data, output_dir)

        print(f'\n图表生成完成！')
        print(f'共生成了 {len(chart_files)} 个图表文件：')
        for chart_file in chart_files:
            print(f'  - {os.path.basename(chart_file)}')

    except Exception as e:
        print(f'处理过程中发生错误: {str(e)}')

if __name__ == "__main__":
    main()

    # 使用示例：
    # 1. 完整处理（保存Excel + 生成图表）：
    #    python process_data.py
    #
    # 2. 仅生成图表：
    #    from process_data import generate_charts_only
    #    generate_charts_only()
    #
    # 3. 读取Excel数据：
    #    from process_data import read_excel_data
    #    read_excel_data('filtered_data.xlsx')
    #
    # 4. 处理其他文件：
    #    from process_data import filter_and_format_data, create_line_charts
    #    formatted_data, numeric_data = filter_and_format_data('your_file.txt', 'output.xlsx')
    #    create_line_charts(numeric_data, 'output_directory')