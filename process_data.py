import pandas as pd
import os
import matplotlib.pyplot as plt
import seaborn as sns
import sys

def process_data(file_path):
    # 读取原始数据
    with open(file_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()

    processed_data = []
    current_machine = None

    for line in lines:
        # 检测当前是哪台机器
        if line.startswith('Machine'):
            current_machine = line.strip()
            continue

        # 跳过包含 'Got BLE CMD1' 的行
        if 'Got BLE CMD1' in line:
            continue

        # 提取 D: G: B: R: 数据
        if 'G:' in line and 'B:' in line and 'R:' in line:
            parts = line.strip().split()
            g_value = float(parts[2].replace('G:', ''))
            b_value = float(parts[3].replace('B:', ''))
            r_value = float(parts[4].replace('R:', '').split()[0])

            processed_data.append([
                current_machine,
                g_value,
                b_value,
                r_value
            ])

    # 调试输出前5条数据
    print("提取的部分数据：")
    for i, data in enumerate(processed_data[:5]):
        print(f"{i+1}: {data}")

    # 创建DataFrame
    df = pd.DataFrame(processed_data, columns=['Machine', 'G', 'B', 'R'])

    # 导出到Excel
    excel_path = os.path.join(os.path.dirname(file_path), 'processed_data.xlsx')
    df.to_excel(excel_path, index=False)

    # 数据可视化
    plt.figure(figsize=(14, 8))

    # 将数据转换为长格式，便于绘图
    df_long = pd.melt(df, id_vars=['Machine'], value_vars=['G', 'B', 'R'],
                     var_name='Parameter', value_name='Value')

    # 添加一个组合标签用于图例
    df_long['Legend'] = df_long['Machine'] + ' - ' + df_long['Parameter']

    # 绘图：每个组合使用不同颜色
    sns.lineplot(x=df_long.index, y='Value', hue='Legend', data=df_long, marker='o')

    plt.title('不同机器的 G、B、R 值变化趋势')
    plt.xlabel('测试次数')
    plt.ylabel('数值')
    plt.legend(title='机器 - 参数')
    plt.xticks(rotation=45)
    plt.tight_layout()

    # 保存图表
    chart_path = os.path.join(os.path.dirname(file_path), 'data_chart.png')
    plt.savefig(chart_path)
    plt.close()

    return excel_path, chart_path


def process_experiment_data(file_path, output_excel_path, output_chart_path, machine_name):
    """
    处理实验数据并生成图表
    :param file_path: 原始数据文件路径
    :param output_excel_path: 输出Excel文件路径
    :param output_chart_path: 输出图表路径
    :param machine_name: 机器名称 (如 "MachineA", "MachineB", "Machinea", "Machineb")
    :return: None
    """
    # 读取原始数据
    with open(file_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()

    processed_data = []

    for line in lines:
        if 'Got BLE CMD49' in line:
            continue

        if 'CH:1' in line:
            # 提取 D: G: B: 数据
            parts = line.strip().split()
            d_value = float(parts[2].replace('D:', '').replace(',', '.'))
            g_value = float(parts[3].replace('G:', '').replace(',', '.'))
            b_value = float(parts[4].replace('B:', '').replace(',', '.'))

            processed_data.append([d_value, g_value, b_value])

    # 创建DataFrame
    df = pd.DataFrame(processed_data, columns=['D', 'G', 'B'])

    # 添加机器列
    df['Machine'] = machine_name

    # 导出到Excel
    if not os.path.exists(output_excel_path):
        # 如果文件不存在，则创建新文件并写入表头
        df.to_excel(output_excel_path, index=False, header=True)
    else:
        # 如果文件存在，则追加数据且不写入表头
        with pd.ExcelWriter(output_excel_path, mode='a', engine='openpyxl') as writer:
            df.to_excel(writer, index=False, header=False)

    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei']  # 使用黑体显示中文
    plt.rcParams['axes.unicode_minus'] = False  # 正常显示负号

    # 绘制折线图
    plt.figure(figsize=(14, 8))
    
    # 明确绘制每条线并设置标签
    sns.lineplot(x=df.index, y='D', data=df, label='D 值')
    sns.lineplot(x=df.index, y='G', data=df, label='G 值')
    sns.lineplot(x=df.index, y='B', data=df, label='B 值')

    plt.title('实验数据变化趋势')
    plt.xlabel('测试次数')
    plt.ylabel('数值')
    plt.legend()
    plt.xticks(rotation=45)
    plt.tight_layout()

    # 保存图表
    plt.savefig(output_chart_path)
    plt.close()

def filter_and_format_data(file_path, output_file_path=None):
    """
    过滤并格式化数据为指定格式：
    MachineA
    G:1108   B:619   R:6.64

    :param file_path: 输入文件路径
    :param output_file_path: 输出文件路径（可选，如果不提供则只打印到控制台）
    :return: 格式化后的数据字典
    """
    # 读取原始数据
    with open(file_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()

    formatted_data = {}
    current_machine = None

    for line in lines:
        line = line.strip()

        # 检测当前是哪台机器
        if line.startswith('Machine'):
            current_machine = line
            if current_machine not in formatted_data:
                formatted_data[current_machine] = []
            continue

        # 跳过包含 'Got BLE CMD1' 的行
        if 'Got BLE CMD1' in line:
            continue

        # 提取 G: B: R: 数据
        if 'G:' in line and 'B:' in line and 'R:' in line and current_machine:
            parts = line.split()

            # 找到G、B、R的值
            g_value = None
            b_value = None
            r_value = None

            for part in parts:
                if part.startswith('G:'):
                    g_value = part.replace('G:', '')
                elif part.startswith('B:'):
                    b_value = part.replace('B:', '')
                elif part.startswith('R:'):
                    r_value = part.replace('R:', '')

            if g_value and b_value and r_value:
                formatted_line = f"G:{g_value}   B:{b_value}   R:{r_value}"
                formatted_data[current_machine].append(formatted_line)

    # 输出结果
    output_lines = []
    print("=" * 50)
    print("过滤并格式化后的数据：")
    print("=" * 50)

    for machine, data_lines in formatted_data.items():
        print(f"\n{machine}")
        output_lines.append(machine)

        for data_line in data_lines:
            print(data_line)
            output_lines.append(data_line)

        output_lines.append("")  # 添加空行分隔不同机器

    # 如果提供了输出文件路径，则保存到文件
    if output_file_path:
        with open(output_file_path, 'w', encoding='utf-8') as f:
            for line in output_lines:
                f.write(line + '\n')
        print(f"\n数据已保存到文件: {output_file_path}")

    return formatted_data

def main():
    # 默认输入文件路径
    input_file = r'c:\Users\<USER>\Desktop\交接资料\测试数据\data.txt'

    # 输出文件路径
    output_file = os.path.join(os.path.dirname(input_file), 'filtered_data.txt')

    print(f'正在处理文件: {input_file}')

    try:
        # 过滤并格式化数据
        formatted_data = filter_and_format_data(input_file, output_file)

        print(f'\n处理完成！')
        print(f'共处理了 {len(formatted_data)} 台机器的数据')

        # 显示每台机器的数据条数
        for machine, data_lines in formatted_data.items():
            print(f'{machine}: {len(data_lines)} 条数据')

    except Exception as e:
        print(f'处理过程中发生错误: {str(e)}')

if __name__ == "__main__":
    main()