import pandas as pd
import os
import matplotlib.pyplot as plt
import seaborn as sns
import sys

def process_data(file_path):
    # 读取原始数据
    with open(file_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()

    processed_data = []
    current_machine = None

    for line in lines:
        # 检测当前是哪台机器
        if line.startswith('Machine'):
            current_machine = line.strip()
            continue

        # 跳过包含 'Got BLE CMD1' 的行
        if 'Got BLE CMD1' in line:
            continue

        # 提取 D: G: B: R: 数据
        if 'G:' in line and 'B:' in line and 'R:' in line:
            parts = line.strip().split()
            g_value = float(parts[2].replace('G:', ''))
            b_value = float(parts[3].replace('B:', ''))
            r_value = float(parts[4].replace('R:', '').split()[0])

            processed_data.append([
                current_machine,
                g_value,
                b_value,
                r_value
            ])

    # 调试输出前5条数据
    print("提取的部分数据：")
    for i, data in enumerate(processed_data[:5]):
        print(f"{i+1}: {data}")

    # 创建DataFrame
    df = pd.DataFrame(processed_data, columns=['Machine', 'G', 'B', 'R'])

    # 导出到Excel
    excel_path = os.path.join(os.path.dirname(file_path), 'processed_data.xlsx')
    df.to_excel(excel_path, index=False)

    # 数据可视化
    plt.figure(figsize=(14, 8))

    # 将数据转换为长格式，便于绘图
    df_long = pd.melt(df, id_vars=['Machine'], value_vars=['G', 'B', 'R'],
                     var_name='Parameter', value_name='Value')

    # 添加一个组合标签用于图例
    df_long['Legend'] = df_long['Machine'] + ' - ' + df_long['Parameter']

    # 绘图：每个组合使用不同颜色
    sns.lineplot(x=df_long.index, y='Value', hue='Legend', data=df_long, marker='o')

    plt.title('不同机器的 G、B、R 值变化趋势')
    plt.xlabel('测试次数')
    plt.ylabel('数值')
    plt.legend(title='机器 - 参数')
    plt.xticks(rotation=45)
    plt.tight_layout()

    # 保存图表
    chart_path = os.path.join(os.path.dirname(file_path), 'data_chart.png')
    plt.savefig(chart_path)
    plt.close()

    return excel_path, chart_path


def process_experiment_data(file_path, output_excel_path, output_chart_path, machine_name):
    """
    处理实验数据并生成图表
    :param file_path: 原始数据文件路径
    :param output_excel_path: 输出Excel文件路径
    :param output_chart_path: 输出图表路径
    :param machine_name: 机器名称 (如 "MachineA", "MachineB", "Machinea", "Machineb")
    :return: None
    """
    # 读取原始数据
    with open(file_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()

    processed_data = []

    for line in lines:
        if 'Got BLE CMD49' in line:
            continue

        if 'CH:1' in line:
            # 提取 D: G: B: 数据
            parts = line.strip().split()
            d_value = float(parts[2].replace('D:', '').replace(',', '.'))
            g_value = float(parts[3].replace('G:', '').replace(',', '.'))
            b_value = float(parts[4].replace('B:', '').replace(',', '.'))

            processed_data.append([d_value, g_value, b_value])

    # 创建DataFrame
    df = pd.DataFrame(processed_data, columns=['D', 'G', 'B'])

    # 添加机器列
    df['Machine'] = machine_name

    # 导出到Excel
    if not os.path.exists(output_excel_path):
        # 如果文件不存在，则创建新文件并写入表头
        df.to_excel(output_excel_path, index=False, header=True)
    else:
        # 如果文件存在，则追加数据且不写入表头
        with pd.ExcelWriter(output_excel_path, mode='a', engine='openpyxl') as writer:
            df.to_excel(writer, index=False, header=False)

    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei']  # 使用黑体显示中文
    plt.rcParams['axes.unicode_minus'] = False  # 正常显示负号

    # 绘制折线图
    plt.figure(figsize=(14, 8))
    
    # 明确绘制每条线并设置标签
    sns.lineplot(x=df.index, y='D', data=df, label='D 值')
    sns.lineplot(x=df.index, y='G', data=df, label='G 值')
    sns.lineplot(x=df.index, y='B', data=df, label='B 值')

    plt.title('实验数据变化趋势')
    plt.xlabel('测试次数')
    plt.ylabel('数值')
    plt.legend()
    plt.xticks(rotation=45)
    plt.tight_layout()

    # 保存图表
    plt.savefig(output_chart_path)
    plt.close()

def main():
    # 默认输入文件路径
    input_file = r'c:\Users\<USER>\Desktop\交接资料\测试数据\data.txt'

    # 输出Excel和图表路径
    output_excel_path = os.path.join(os.path.dirname(input_file), 'experiment_data.xlsx')
    output_chart_path = os.path.join(os.path.dirname(input_file), 'experiment_chart.png')

    print(f'正在处理文件: {input_file}')

    try:
        # 示例处理四台机器的数据
        machines = ['MachineA', 'MachineB', 'Machinea', 'Machineb']
        for machine in machines:
            # 假设每台机器的数据在同一个文件中，这里可以根据实际调整
            process_experiment_data(input_file, output_excel_path, f'{os.path.splitext(output_chart_path)[0]}_{machine}.png', machine)

        print(f'数据已成功处理并保存到Excel文件: {output_excel_path}')
        print(f'图表已生成并保存到: {output_chart_path}')
    except Exception as e:
        print(f'处理过程中发生错误: {str(e)}')

if __name__ == "__main__":
    main()